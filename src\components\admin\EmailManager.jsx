import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaEnvelope,
  FaPlus,
  FaEye,
  FaFilter,
  FaDownload,
  FaCheck,
  FaTimes,
  FaClock,
  FaExclamationTriangle,
} from "react-icons/fa";
import { colors, spacing, breakpoints } from "../../styles";
import { useAuth } from "../../context/AuthContext";
import { httpsCallable } from "firebase/functions";
import { functions } from "../../firebase/config";
import EmailComposer from "./EmailComposer";
import EmailTester from "./EmailTester";

const Container = styled.div`
  padding: ${spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: ${spacing.xl};
  flex-wrap: wrap;
  gap: ${spacing.md};

  @media (max-width: ${breakpoints.md}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const Title = styled.h1`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${spacing.md};
`;

const Controls = styled.div`
  display: flex;
  gap: ${spacing.md};
  align-items: center;
  flex-wrap: wrap;
`;

const Button = styled(motion.button).withConfig({
  shouldForwardProp: (prop) => !["variant"].includes(prop),
})`
  background: ${(props) =>
    props.variant === "primary"
      ? `linear-gradient(135deg, ${colors.primary.main}, ${colors.primary.dark})`
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.lightest};
  color: ${(props) =>
    props.variant === "primary"
      ? "white"
      : props.darkMode
      ? colors.neutral.light
      : colors.neutral.dark};
  border: none;
  padding: ${spacing.md} ${spacing.lg};
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
`;

const FilterSection = styled.div`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  margin-bottom: ${spacing.xl};
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${spacing.md};
  margin-bottom: ${spacing.md};
`;

const Select = styled.select`
  padding: ${spacing.md};
  border: 2px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 8px;
  background: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const EmailGrid = styled.div`
  display: grid;
  gap: ${spacing.md};
`;

const EmailCard = styled(motion.div)`
  background: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.lg};
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid
    ${(props) =>
      props.status === "sent"
        ? colors.success.main
        : props.status === "failed"
        ? colors.error.main
        : props.status === "logged_only"
        ? colors.warning.main
        : colors.neutral.light};
`;

const EmailHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${spacing.md};
  flex-wrap: wrap;
  gap: ${spacing.sm};
`;

const EmailInfo = styled.div`
  flex: 1;
`;

const EmailTo = styled.div`
  font-weight: 600;
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.darkest};
  margin-bottom: ${spacing.xs};
`;

const EmailSubject = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.9rem;
`;

const EmailMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  flex-wrap: wrap;
`;

const StatusBadge = styled.div`
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${spacing.xs};

  ${(props) =>
    props.status === "sent" &&
    `
    background: ${colors.success.light}20;
    color: ${colors.success.dark};
  `}

  ${(props) =>
    props.status === "failed" &&
    `
    background: ${colors.error.light}20;
    color: ${colors.error.dark};
  `}
  
  ${(props) =>
    props.status === "logged_only" &&
    `
    background: ${colors.warning.light}20;
    color: ${colors.warning.dark};
  `}
  
  ${(props) =>
    props.status === "pending" &&
    `
    background: ${colors.neutral.light}20;
    color: ${colors.neutral.dark};
  `}
`;

const Timestamp = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.8rem;
`;

const EmailContent = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: 0.9rem;
  line-height: 1.5;
  margin-top: ${spacing.md};
  padding-top: ${spacing.md};
  border-top: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
`;

const LoadingSpinner = styled(motion.div)`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${spacing.xl};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${spacing.xl};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const EmailManager = ({ darkMode }) => {
  const [emails, setEmails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showComposer, setShowComposer] = useState(false);
  const [filters, setFilters] = useState({
    type: "all",
    status: "all",
  });
  const { currentUser, isAuthenticated, isAdmin, isEditor } = useAuth();

  useEffect(() => {
    console.log("🔍 EmailManager useEffect triggered");
    console.log("🔍 currentUser:", currentUser);
    console.log("🔍 isAuthenticated:", isAuthenticated);
    console.log("🔍 isAdmin:", isAdmin);
    console.log("🔍 isEditor:", isEditor);
    console.log("🔍 Filters:", filters);

    if (isAuthenticated && (isAdmin || isEditor)) {
      console.log(
        "🔍 User is authenticated and has admin/editor privileges, calling loadEmails..."
      );
      loadEmails();
    } else {
      console.log("🔍 User not authenticated or lacks privileges");
      console.log("🔍 - isAuthenticated:", isAuthenticated);
      console.log("🔍 - isAdmin:", isAdmin);
      console.log("🔍 - isEditor:", isEditor);
    }
  }, [filters, isAuthenticated, isAdmin, isEditor]);

  const loadEmails = async () => {
    console.log("🚀 loadEmails function called!");
    try {
      console.log("🚀 Setting loading to true...");
      setLoading(true);

      // Check if user is authenticated and has proper privileges
      if (!isAuthenticated) {
        console.error("❌ User not authenticated");
        return;
      }

      if (!isAdmin && !isEditor) {
        console.error("❌ User lacks admin/editor privileges");
        return;
      }

      console.log(
        "✅ Current user:",
        currentUser?.email || "No email available"
      );
      console.log("✅ User object:", currentUser);
      console.log("✅ isAuthenticated:", isAuthenticated);
      console.log("✅ isAdmin:", isAdmin);
      console.log("✅ isEditor:", isEditor);
      console.log("🔄 Loading emails...");

      // Force token refresh to ensure we have a valid auth token
      try {
        const { auth } = await import("../../firebase/config");
        const currentUser = auth.currentUser;
        if (currentUser) {
          console.log("🔄 Forcing token refresh...");
          await currentUser.getIdToken(true); // Force refresh
          console.log("✅ Token refreshed successfully");
        } else {
          console.error("❌ No current user found in Firebase Auth");
        }
      } catch (tokenError) {
        console.error("❌ Token refresh failed:", tokenError);
      }

      const getEmailLogs = httpsCallable(functions, "getEmailLogs");
      console.log("About to call getEmailLogs function...");

      const result = await getEmailLogs({
        limit: 50,
        type: filters.type !== "all" ? filters.type : undefined,
      });

      console.log("Email logs result:", result.data);

      // Handle the data more safely
      const emailLogs = result.data.logs || [];
      console.log("Processing", emailLogs.length, "email logs");

      // Process each email log safely
      const processedEmails = emailLogs.map((email, index) => {
        try {
          return {
            ...email,
            // Safely handle timestamps
            timestamp:
              email.timestamp || email.createdAt || new Date().toISOString(),
            createdAt: email.createdAt || new Date().toISOString(),
          };
        } catch (emailError) {
          console.error(`Error processing email ${index}:`, emailError);
          return {
            ...email,
            timestamp: new Date().toISOString(),
            createdAt: new Date().toISOString(),
          };
        }
      });

      console.log("Successfully processed", processedEmails.length, "emails");
      setEmails(processedEmails);
    } catch (error) {
      console.error("Error loading emails:", error);
      console.error("Error details:", error.code, error.message);
      console.error("Full error object:", error);

      // Show user-friendly error message
      if (error.code === "functions/permission-denied") {
        console.error(
          "🚨 PERMISSION DENIED: User doesn't have admin/editor privileges"
        );
        alert(
          "Permission denied: You need admin or editor privileges to view emails. Contact system administrator."
        );
      } else if (error.code === "functions/unauthenticated") {
        console.error("🚨 UNAUTHENTICATED: User not logged in properly");
        alert("Authentication error: Please log out and log back in.");
      } else {
        console.error("🚨 UNKNOWN ERROR:", error.message);
        alert(`Error loading emails: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "sent":
        return <FaCheck />;
      case "failed":
        return <FaTimes />;
      case "logged_only":
        return <FaExclamationTriangle />;
      default:
        return <FaClock />;
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "Unknown";
    return new Date(timestamp).toLocaleString();
  };

  const filteredEmails = emails.filter((email) => {
    if (filters.status !== "all" && email.status !== filters.status)
      return false;
    return true;
  });

  return (
    <Container>
      <Header>
        <Title darkMode={darkMode}>
          <FaEnvelope />
          Email Management
        </Title>
        <Controls>
          <Button
            variant="primary"
            onClick={() => setShowComposer(true)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FaPlus /> Compose Email
          </Button>
          <Button
            darkMode={darkMode}
            onClick={loadEmails}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FaDownload /> Refresh
          </Button>
        </Controls>
      </Header>

      <FilterSection darkMode={darkMode}>
        <FilterGrid>
          <div>
            <label
              style={{
                display: "block",
                marginBottom: spacing.sm,
                color: darkMode ? colors.neutral.light : colors.neutral.dark,
                fontWeight: 500,
              }}
            >
              Email Type
            </label>
            <Select
              darkMode={darkMode}
              value={filters.type}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, type: e.target.value }))
              }
            >
              <option value="all">All Types</option>
              <option value="general">General</option>
              <option value="order">Order Confirmations</option>
              <option value="support">Support</option>
              <option value="welcome">Welcome Emails</option>
            </Select>
          </div>
          <div>
            <label
              style={{
                display: "block",
                marginBottom: spacing.sm,
                color: darkMode ? colors.neutral.light : colors.neutral.dark,
                fontWeight: 500,
              }}
            >
              Status
            </label>
            <Select
              darkMode={darkMode}
              value={filters.status}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, status: e.target.value }))
              }
            >
              <option value="all">All Statuses</option>
              <option value="sent">Sent</option>
              <option value="logged_only">Logged Only</option>
              <option value="failed">Failed</option>
              <option value="pending">Pending</option>
            </Select>
          </div>
        </FilterGrid>
      </FilterSection>

      {loading ? (
        <LoadingSpinner darkMode={darkMode}>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
          >
            <FaClock />
          </motion.div>
          Loading emails...
        </LoadingSpinner>
      ) : filteredEmails.length === 0 ? (
        <EmptyState darkMode={darkMode}>
          <FaEnvelope
            size={48}
            style={{ marginBottom: spacing.md, opacity: 0.5 }}
          />
          <h3>No emails found</h3>
          <p>No emails match your current filters.</p>
        </EmptyState>
      ) : (
        <EmailGrid>
          <AnimatePresence>
            {filteredEmails.map((email, index) => (
              <EmailCard
                key={email.id}
                darkMode={darkMode}
                status={email.status}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <EmailHeader>
                  <EmailInfo>
                    <EmailTo darkMode={darkMode}>To: {email.to}</EmailTo>
                    <EmailSubject darkMode={darkMode}>
                      {email.subject}
                    </EmailSubject>
                  </EmailInfo>
                  <EmailMeta>
                    <StatusBadge status={email.status}>
                      {getStatusIcon(email.status)}
                      {email.status}
                    </StatusBadge>
                    <Timestamp darkMode={darkMode}>
                      {formatTimestamp(email.timestamp)}
                    </Timestamp>
                  </EmailMeta>
                </EmailHeader>
                {email.content && (
                  <EmailContent darkMode={darkMode}>
                    {email.content.length > 200
                      ? `${email.content.substring(0, 200)}...`
                      : email.content}
                  </EmailContent>
                )}
              </EmailCard>
            ))}
          </AnimatePresence>
        </EmailGrid>
      )}

      <EmailTester darkMode={darkMode} />

      <EmailComposer
        isOpen={showComposer}
        onClose={() => setShowComposer(false)}
        darkMode={darkMode}
      />
    </Container>
  );
};

export default EmailManager;
